/**
 * 注册批次1：AI与计算机视觉系统节点注册表
 * 注册61个AI与计算机视觉系统节点到编辑器
 * 
 * 节点分类：
 * - 深度学习扩展节点：11个
 * - 机器学习扩展节点：8个
 * - AI工具节点：10个
 * - 自然语言处理节点：7个
 * - 模型管理节点：25个
 * 
 * 创建时间：2025年7月8日
 * 负责团队：AI系统团队
 */

import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入深度学习扩展节点（11个）
import {
  TransformerModelNode,
  GANModelNode,
  VAEModelNode,
  AttentionMechanismNode,
  EmbeddingLayerNode,
  DropoutLayerNode,
  BatchNormalizationNode,
  ActivationFunctionNode
} from '../nodes/ai/DeepLearningNodes4';

import {
  LossFunctionNode,
  OptimizerNode
} from '../nodes/ai/DeepLearningNodes5';

// 导入机器学习扩展节点（8个）
import {
  RandomForestNode,
  SupportVectorMachineNode,
  KMeansClusteringNode,
  PCANode,
  LinearRegressionNode,
  LogisticRegressionNode,
  DecisionTreeNode,
  EnsembleMethodNode
} from '../nodes/ai/MachineLearningExtensionNodes';

// 导入AI工具节点（10个）
import {
  ModelDeploymentNode,
  ModelMonitoringNode,
  ModelVersioningNode,
  AutoMLNode,
  ExplainableAINode,
  AIEthicsNode,
  ModelCompressionNode,
  QuantizationNode,
  PruningNode,
  DistillationNode
} from '../nodes/ai/AIToolNodes';

// 导入自然语言处理节点（7个）
import {
  TextClassificationNode,
  NamedEntityRecognitionNode,
  SentimentAnalysisNode,
  TextSummarizationNode,
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
} from '../nodes/ai/NaturalLanguageProcessingNodes';

// 导入模型管理节点（5个实际存在的）
import {
  ModelRegistryNode,
  ModelValidationNode,
  ModelTestingNode,
  ModelBenchmarkNode,
  ModelComparisonNode
} from '../nodes/ai/ModelManagementNodes';

/**
 * AI与计算机视觉系统节点注册表
 */
export class AIExtensionNodesRegistry {
  private static instance: AIExtensionNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AIExtensionNodesRegistry {
    if (!AIExtensionNodesRegistry.instance) {
      AIExtensionNodesRegistry.instance = new AIExtensionNodesRegistry();
    }
    return AIExtensionNodesRegistry.instance;
  }

  /**
   * 注册所有AI与计算机视觉系统节点（41个）
   */
  public registerAllNodes(): void {
    if (this.registered) {
      Debug.log('AIExtensionNodesRegistry', 'AI与计算机视觉系统节点已注册，跳过重复注册');
      return;
    }

    Debug.log('AIExtensionNodesRegistry', '开始注册AI与计算机视觉系统节点...');

    try {
      // 注册深度学习扩展节点（11个）
      this.registerDeepLearningExtensionNodes();

      // 注册机器学习扩展节点（8个）
      this.registerMachineLearningExtensionNodes();

      // 注册AI工具节点（10个）
      this.registerAIToolNodes();

      // 注册自然语言处理节点（7个）
      this.registerNaturalLanguageProcessingNodes();

      // 注册模型管理节点（5个）
      this.registerModelManagementNodes();

      this.registered = true;

      Debug.log('AIExtensionNodesRegistry', 'AI与计算机视觉系统节点注册完成');
      Debug.log('AIExtensionNodesRegistry', `深度学习扩展节点：11个`);
      Debug.log('AIExtensionNodesRegistry', `机器学习扩展节点：8个`);
      Debug.log('AIExtensionNodesRegistry', `AI工具节点：10个`);
      Debug.log('AIExtensionNodesRegistry', `自然语言处理节点：7个`);
      Debug.log('AIExtensionNodesRegistry', `模型管理节点：5个`);
      Debug.log('AIExtensionNodesRegistry', `总计：41个节点`);

    } catch (error) {
      Debug.error('AIExtensionNodesRegistry', 'AI与计算机视觉系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册深度学习扩展节点（11个）
   */
  private registerDeepLearningExtensionNodes(): void {
    Debug.log('AIExtensionNodesRegistry', '注册深度学习扩展节点...');

    // Transformer模型节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/transformerModel',
      name: 'Transformer模型',
      description: '基于注意力机制的Transformer模型',
      category: NodeCategory.AI,
      nodeClass: TransformerModelNode,
      color: '#FF6B35',
      icon: 'transformer'
    }));

    // GAN模型节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/ganModel',
      name: 'GAN模型',
      description: '生成对抗网络模型',
      category: NodeCategory.AI,
      nodeClass: GANModelNode,
      color: '#FF6B35',
      icon: 'gan'
    }));

    // VAE模型节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/vaeModel',
      name: 'VAE模型',
      description: '变分自编码器模型',
      category: NodeCategory.AI,
      nodeClass: VAEModelNode,
      color: '#FF6B35',
      icon: 'vae'
    }));

    // 注意力机制节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/attentionMechanism',
      name: '注意力机制',
      description: '注意力机制计算',
      category: NodeCategory.AI,
      nodeClass: AttentionMechanismNode,
      color: '#FF6B35',
      icon: 'attention'
    }));

    // 嵌入层节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/embeddingLayer',
      name: '嵌入层',
      description: '词嵌入和特征嵌入层',
      category: NodeCategory.AI,
      nodeClass: EmbeddingLayerNode,
      color: '#FF6B35',
      icon: 'embedding'
    }));

    // Dropout层节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/dropoutLayer',
      name: 'Dropout层',
      description: 'Dropout正则化层',
      category: NodeCategory.AI,
      nodeClass: DropoutLayerNode,
      color: '#FF6B35',
      icon: 'dropout'
    }));

    // 批归一化节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/batchNormalization',
      name: '批归一化',
      description: '批归一化层',
      category: NodeCategory.AI,
      nodeClass: BatchNormalizationNode,
      color: '#FF6B35',
      icon: 'batch-norm'
    }));

    // 激活函数节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/activationFunction',
      name: '激活函数',
      description: '神经网络激活函数',
      category: NodeCategory.AI,
      nodeClass: ActivationFunctionNode,
      color: '#FF6B35',
      icon: 'activation'
    }));

    // 损失函数节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/lossFunction',
      name: '损失函数',
      description: '模型训练损失函数',
      category: NodeCategory.AI,
      nodeClass: LossFunctionNode,
      color: '#FF6B35',
      icon: 'loss'
    }));

    // 优化器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/optimizer',
      name: '优化器',
      description: '模型参数优化器',
      category: NodeCategory.AI,
      nodeClass: OptimizerNode,
      color: '#FF6B35',
      icon: 'optimizer'
    }));

    Debug.log('AIExtensionNodesRegistry', '深度学习扩展节点注册完成：11个');
  }

  /**
   * 注册机器学习扩展节点（8个）
   */
  private registerMachineLearningExtensionNodes(): void {
    Debug.log('AIExtensionNodesRegistry', '注册机器学习扩展节点...');

    // 随机森林节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/randomForest',
      name: '随机森林',
      description: '随机森林集成学习算法',
      category: NodeCategory.AI,
      nodeClass: RandomForestNode,
      color: '#4CAF50',
      icon: 'random-forest'
    }));

    // 支持向量机节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/supportVectorMachine',
      name: '支持向量机',
      description: 'SVM分类和回归算法',
      category: NodeCategory.AI,
      nodeClass: SupportVectorMachineNode,
      color: '#4CAF50',
      icon: 'svm'
    }));

    // K均值聚类节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/kMeansClustering',
      name: 'K均值聚类',
      description: 'K-means无监督聚类算法',
      category: NodeCategory.AI,
      nodeClass: KMeansClusteringNode,
      color: '#4CAF50',
      icon: 'kmeans'
    }));

    // 主成分分析节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/pca',
      name: '主成分分析',
      description: 'PCA降维算法',
      category: NodeCategory.AI,
      nodeClass: PCANode,
      color: '#4CAF50',
      icon: 'pca'
    }));

    // 线性回归节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/linearRegression',
      name: '线性回归',
      description: '线性回归算法',
      category: NodeCategory.AI,
      nodeClass: LinearRegressionNode,
      color: '#4CAF50',
      icon: 'linear-regression'
    }));

    // 逻辑回归节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/logisticRegression',
      name: '逻辑回归',
      description: '逻辑回归分类算法',
      category: NodeCategory.AI,
      nodeClass: LogisticRegressionNode,
      color: '#4CAF50',
      icon: 'logistic-regression'
    }));

    // 决策树节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/decisionTree',
      name: '决策树',
      description: '决策树分类和回归算法',
      category: NodeCategory.AI,
      nodeClass: DecisionTreeNode,
      color: '#4CAF50',
      icon: 'decision-tree'
    }));

    // 集成方法节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ml/ensembleMethod',
      name: '集成方法',
      description: '模型集成学习方法',
      category: NodeCategory.AI,
      nodeClass: EnsembleMethodNode,
      color: '#4CAF50',
      icon: 'ensemble'
    }));

    Debug.log('AIExtensionNodesRegistry', '机器学习扩展节点注册完成：8个');
  }

  /**
   * 注册AI工具节点（10个）
   */
  private registerAIToolNodes(): void {
    Debug.log('AIExtensionNodesRegistry', '注册AI工具节点...');

    // 模型部署节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/modelDeployment',
      name: '模型部署',
      description: 'AI模型部署到生产环境',
      category: NodeCategory.AI,
      nodeClass: ModelDeploymentNode,
      color: '#2196F3',
      icon: 'deployment'
    }));

    // 模型监控节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/modelMonitoring',
      name: '模型监控',
      description: '监控AI模型运行状态',
      category: NodeCategory.AI,
      nodeClass: ModelMonitoringNode,
      color: '#2196F3',
      icon: 'monitoring'
    }));

    // 模型版本管理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/modelVersioning',
      name: '模型版本管理',
      description: '管理AI模型版本',
      category: NodeCategory.AI,
      nodeClass: ModelVersioningNode,
      color: '#2196F3',
      icon: 'versioning'
    }));

    // 自动机器学习节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/autoML',
      name: '自动机器学习',
      description: 'AutoML自动化机器学习',
      category: NodeCategory.AI,
      nodeClass: AutoMLNode,
      color: '#2196F3',
      icon: 'automl'
    }));

    // 可解释AI节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/explainableAI',
      name: '可解释AI',
      description: '解释AI模型决策过程',
      category: NodeCategory.AI,
      nodeClass: ExplainableAINode,
      color: '#2196F3',
      icon: 'explainable'
    }));

    // AI伦理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/aiEthics',
      name: 'AI伦理',
      description: '检查AI模型伦理问题',
      category: NodeCategory.AI,
      nodeClass: AIEthicsNode,
      color: '#2196F3',
      icon: 'ethics'
    }));

    // 模型压缩节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/modelCompression',
      name: '模型压缩',
      description: '压缩AI模型大小',
      category: NodeCategory.AI,
      nodeClass: ModelCompressionNode,
      color: '#2196F3',
      icon: 'compression'
    }));

    // 量化节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/quantization',
      name: '量化',
      description: '模型参数量化',
      category: NodeCategory.AI,
      nodeClass: QuantizationNode,
      color: '#2196F3',
      icon: 'quantization'
    }));

    // 剪枝节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/pruning',
      name: '剪枝',
      description: '模型结构剪枝',
      category: NodeCategory.AI,
      nodeClass: PruningNode,
      color: '#2196F3',
      icon: 'pruning'
    }));

    // 知识蒸馏节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'ai/distillation',
      name: '知识蒸馏',
      description: '知识蒸馏技术',
      category: NodeCategory.AI,
      nodeClass: DistillationNode,
      color: '#2196F3',
      icon: 'distillation'
    }));

    Debug.log('AIExtensionNodesRegistry', 'AI工具节点注册完成：10个');
  }

  /**
   * 注册自然语言处理节点（7个）
   */
  private registerNaturalLanguageProcessingNodes(): void {
    Debug.log('AIExtensionNodesRegistry', '注册自然语言处理节点...');

    // 文本分类节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/textClassification',
      name: '文本分类',
      description: '对文本进行分类',
      category: NodeCategory.AI,
      nodeClass: TextClassificationNode,
      color: '#9C27B0',
      icon: 'text-classification'
    }));

    // 命名实体识别节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/namedEntityRecognition',
      name: '命名实体识别',
      description: '识别文本中的命名实体',
      category: NodeCategory.AI,
      nodeClass: NamedEntityRecognitionNode,
      color: '#9C27B0',
      icon: 'entity-recognition'
    }));

    // 情感分析节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/sentimentAnalysis',
      name: '情感分析',
      description: '分析文本情感倾向',
      category: NodeCategory.AI,
      nodeClass: SentimentAnalysisNode,
      color: '#9C27B0',
      icon: 'sentiment'
    }));

    // 文本摘要节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/textSummarization',
      name: '文本摘要',
      description: '生成文本摘要',
      category: NodeCategory.AI,
      nodeClass: TextSummarizationNode,
      color: '#9C27B0',
      icon: 'summarization'
    }));

    // 机器翻译节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/machineTranslation',
      name: '机器翻译',
      description: '翻译文本到目标语言',
      category: NodeCategory.AI,
      nodeClass: MachineTranslationNode,
      color: '#9C27B0',
      icon: 'translation'
    }));

    // 问答系统节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/questionAnswering',
      name: '问答系统',
      description: '回答基于文本的问题',
      category: NodeCategory.AI,
      nodeClass: QuestionAnsweringNode,
      color: '#9C27B0',
      icon: 'question-answer'
    }));

    // 文本生成节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'nlp/textGeneration',
      name: '文本生成',
      description: '生成新的文本内容',
      category: NodeCategory.AI,
      nodeClass: TextGenerationNode,
      color: '#9C27B0',
      icon: 'text-generation'
    }));

    Debug.log('AIExtensionNodesRegistry', '自然语言处理节点注册完成：7个');
  }

  /**
   * 注册模型管理节点（5个）
   */
  private registerModelManagementNodes(): void {
    Debug.log('AIExtensionNodesRegistry', '注册模型管理节点...');

    // 模型注册表节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/registry',
      name: '模型注册表',
      description: '注册和管理AI模型',
      category: NodeCategory.AI,
      nodeClass: ModelRegistryNode,
      color: '#FF9800',
      icon: 'model-registry'
    }));

    // 模型验证节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/validation',
      name: '模型验证',
      description: '验证AI模型性能和质量',
      category: NodeCategory.AI,
      nodeClass: ModelValidationNode,
      color: '#FF9800',
      icon: 'model-validation'
    }));

    // 模型测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/testing',
      name: '模型测试',
      description: '执行AI模型测试',
      category: NodeCategory.AI,
      nodeClass: ModelTestingNode,
      color: '#FF9800',
      icon: 'model-testing'
    }));

    // 模型基准测试节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/benchmark',
      name: '模型基准测试',
      description: '执行模型性能基准测试',
      category: NodeCategory.AI,
      nodeClass: ModelBenchmarkNode,
      color: '#FF9800',
      icon: 'model-benchmark'
    }));

    // 模型比较节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'model/comparison',
      name: '模型比较',
      description: '比较多个AI模型性能',
      category: NodeCategory.AI,
      nodeClass: ModelComparisonNode,
      color: '#FF9800',
      icon: 'model-comparison'
    }));

    Debug.log('AIExtensionNodesRegistry', '模型管理节点注册完成：5个');
  }

  /**
   * 获取注册状态
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStats(): any {
    return {
      deepLearningExtension: 11,
      machineLearningExtension: 8,
      aiTools: 10,
      naturalLanguageProcessing: 7,
      modelManagement: 5,
      total: 41
    };
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 深度学习扩展节点
      'ai/transformerModel',
      'ai/ganModel',
      'ai/vaeModel',
      'ai/attentionMechanism',
      'ai/embeddingLayer',
      'ai/dropoutLayer',
      'ai/batchNormalization',
      'ai/activationFunction',
      'ai/lossFunction',
      'ai/optimizer',

      // 机器学习扩展节点
      'ml/randomForest',
      'ml/supportVectorMachine',
      'ml/kMeansClustering',
      'ml/pca',
      'ml/linearRegression',
      'ml/logisticRegression',
      'ml/decisionTree',
      'ml/ensembleMethod',

      // AI工具节点
      'ai/modelDeployment',
      'ai/modelMonitoring',
      'ai/modelVersioning',
      'ai/autoML',
      'ai/explainableAI',
      'ai/aiEthics',
      'ai/modelCompression',
      'ai/quantization',
      'ai/pruning',
      'ai/distillation',

      // 自然语言处理节点
      'nlp/textClassification',
      'nlp/namedEntityRecognition',
      'nlp/sentimentAnalysis',
      'nlp/textSummarization',
      'nlp/machineTranslation',
      'nlp/questionAnswering',
      'nlp/textGeneration',

      // 模型管理节点
      'model/registry',
      'model/validation',
      'model/testing',
      'model/benchmark',
      'model/comparison'
    ];
  }
}

// 创建单例实例
export const aiExtensionNodesRegistry = AIExtensionNodesRegistry.getInstance();

// 导出节点类型常量
export const AI_EXTENSION_NODE_TYPES = {
  // 深度学习扩展节点
  TRANSFORMER_MODEL: 'ai/transformerModel',
  GAN_MODEL: 'ai/ganModel',
  VAE_MODEL: 'ai/vaeModel',
  ATTENTION_MECHANISM: 'ai/attentionMechanism',
  EMBEDDING_LAYER: 'ai/embeddingLayer',
  DROPOUT_LAYER: 'ai/dropoutLayer',
  BATCH_NORMALIZATION: 'ai/batchNormalization',
  ACTIVATION_FUNCTION: 'ai/activationFunction',
  LOSS_FUNCTION: 'ai/lossFunction',
  OPTIMIZER: 'ai/optimizer',

  // 机器学习扩展节点
  RANDOM_FOREST: 'ml/randomForest',
  SUPPORT_VECTOR_MACHINE: 'ml/supportVectorMachine',
  KMEANS_CLUSTERING: 'ml/kMeansClustering',
  PCA: 'ml/pca',
  LINEAR_REGRESSION: 'ml/linearRegression',
  LOGISTIC_REGRESSION: 'ml/logisticRegression',
  DECISION_TREE: 'ml/decisionTree',
  ENSEMBLE_METHOD: 'ml/ensembleMethod',

  // AI工具节点
  MODEL_DEPLOYMENT: 'ai/modelDeployment',
  MODEL_MONITORING: 'ai/modelMonitoring',
  MODEL_VERSIONING: 'ai/modelVersioning',
  AUTO_ML: 'ai/autoML',
  EXPLAINABLE_AI: 'ai/explainableAI',
  AI_ETHICS: 'ai/aiEthics',
  MODEL_COMPRESSION: 'ai/modelCompression',
  QUANTIZATION: 'ai/quantization',
  PRUNING: 'ai/pruning',
  DISTILLATION: 'ai/distillation',

  // 自然语言处理节点
  TEXT_CLASSIFICATION: 'nlp/textClassification',
  NAMED_ENTITY_RECOGNITION: 'nlp/namedEntityRecognition',
  SENTIMENT_ANALYSIS: 'nlp/sentimentAnalysis',
  TEXT_SUMMARIZATION: 'nlp/textSummarization',
  MACHINE_TRANSLATION: 'nlp/machineTranslation',
  QUESTION_ANSWERING: 'nlp/questionAnswering',
  TEXT_GENERATION: 'nlp/textGeneration',

  // 模型管理节点
  MODEL_REGISTRY: 'model/registry',
  MODEL_VALIDATION: 'model/validation',
  MODEL_TESTING: 'model/testing',
  MODEL_BENCHMARK: 'model/benchmark',
  MODEL_COMPARISON: 'model/comparison'
} as const;
